document.addEventListener('DOMContentLoaded',()=>{
    let profileMenu =document.querySelectorAll('[data-toggle=".dropdown"]');
    let dataMenu =document.querySelector('[data-menu]');
    let dataMenuClose =document.querySelector('[data-menu-close]');
    let rightArrow =document.querySelector('.right-arrow');
    let leftArrow= document.querySelector('.left-arrow');
    let currentIndex=0;
    rightArrow.addEventListener('click',()=>{
      let carousel = document.querySelector('.scroll-content');
      currentIndex++;
      console.log(currentIndex,"right");
      if(currentIndex=0){
        currentIndex=carousel.children.length-1;
      }
      if(currentIndex>carousel.children.length-1){
        currentIndex=0;
      }
      console.log(currentIndex,"rightafter");
      carousel.style.transform = `translateX(${currentIndex*100}%)`;
    })
    leftArrow.addEventListener('click',()=>{
    let carousel=document.querySelector('.scroll-content');
    console.log(currentIndex,"left");
    currentIndex--;
    if(currentIndex<0){
        currentIndex=carousel.children.length-1;
    }
    carousel.style.transform= `transalteX(-${currentIndex*100}%)`

    })
    dataMenuClose.addEventListener('click', (e) => {
        let sidebar = document.querySelector(e.target.dataset.menuClose);
        sidebar.classList.remove('show-sidebar');
        let closeButton= document.querySelector('.fa-times');
        let barsButton= document.querySelector('.fa-bars');
        barsButton.style.display = 'block';
        closeButton.style.display = 'none';
    });
    dataMenu.addEventListener('click', (e) => {
        let sidebar = document.querySelector(e.target.dataset.menu);
        sidebar.classList.toggle('show-sidebar');
        let closeButton= document.querySelector('.fa-times');
        let barsButton= document.querySelector('.fa-bars');
        barsButton.style.display = 'none';
        closeButton.style.display = 'block';
    });
    profileMenu.forEach(menu => {
        menu.addEventListener('click', (e) => {
            let dropdown = document.querySelector(e.target.dataset.toggle);
             dropdown.classList.toggle('show');
            let subMenu = document.querySelector('.sub-menu');
            subMenu.classList.remove('show');
        });
    });
    let subMenu =document.querySelectorAll('[data-toggle=".sub-menu"]');
    subMenu.forEach(menu => {
        menu.addEventListener('click', (e) => {
            let dropdown = document.querySelector(e.currentTarget.dataset.toggle);
             dropdown.classList.toggle('show');
        });
    });
})