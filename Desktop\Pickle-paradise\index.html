<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pickle Paradise</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        :root{
            --primary-color: #F5F2E0;
            --secondary-color: rgb(53, 149, 53);
            --tertiary-color: #FFFFFF;
            --title-color: #090A0B;
            --text-color: #474a4d;
        }
        *{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body{
            overflow-x: hidden;
            color: var(--text-color);
        }
        .nav-bar{
            width: 100%;
            background-color: var(--primary-color);
            position: sticky;
            top: 0;
            z-index: 1;
            height: 60px;
        }
        .primary-button{
            background-color: var(--secondary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .header-flex{
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .nav-bar>nav{
            max-width: 1200px;
            margin: auto;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        .main-layout{
          width: 100%;
          overflow-x: hidden;
          overflow-y: auto;
          display: flex; 
        }
        h1,h2,h3{
            color: var(--title-color);
        }
        .overlay p{
            font-size: 0.8rem;
            width: 90%
        }
        .sidebar{
            position: sticky;
            top: 0;
            background-color: var(--tertiary-color);
            display: flex;
            flex-direction: column;
            padding: 10px 20px;
            gap: 20px;
            border-right: 1px solid #ccc;
            height: 100vh;
        }
        .logout-icon{
            flex: 0.3;
            position: absolute;
            bottom: 20px;
        }
        .option, .logout-icon{
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.2s ease;
        }
        .option:hover{
            background-color: #e6e6e6;
        }
        .main-content{
            flex: 1;
            overflow-y: auto; 
            overflow-x: hidden;
            height: 100vh;
            position: relative;
        }
        .background-image{
            position: relative;
            height: 100vh;
            overflow: hidden;
            background-color: #f2f2f2;
        }
        .background-image>img{
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
        .carousel{
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
            width: 100%;
            overflow-x: auto;
            overflow-y: hidden;
            padding: 20px;
            scroll-behavior: auto;
            scroll-snap-type: x mandatory;
            white-space: nowrap;
            position: relative;
        }
        .right-arrow, .left-arrow{
            width: 50px;
            height: 50px;
            background-color: #f2f2f2;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1;
        }
        .right-arrow{
            left: 20px;
        }
        .left-arrow{
            right: 20px;
        }
        .scroll-content{
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            /* scroll-snap-align: start;
            scroll-snap-stop: always; */
        }
        .scroll-content > .card{
            display: flex;
            flex-direction: column;
            gap: 10px;
            /* scroll-snap-align: start;
            scroll-snap-stop: always; */
            min-width: 300px;
            height: 400px;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
        }
        .collection{
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
            justify-content: center;
        }
        .collection-cards{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            width: 100%;
            padding: 20px;
        }
        .card{
            display: flex;
            flex-direction: column;
            gap: 10px;
            min-width: 300px;
            max-width: 300px;
            height: 400px;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            position: relative;
        }
        .background-image{
            width: 100%;
            height: 100%;
            object-fit: cover;
            background-position: center;
        }
        .card > img{
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        .card-body{
            padding: 20px;
            display: flex;
            overflow: hidden;
            flex-direction: column;
            gap: 10px;
            flex: 1;
            justify-content: flex-end;
            overflow: hidden;
        }
        .card-title{
            font-size: 1.2rem;

        }
        .overlay{
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: flex-start;
            gap: 10px;
            padding: 20px;
            color: white;

        }
        .bottom-product-section{
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .bottom-product-section>button{
            background-color: var(--secondary-color);
            border: none;
            color: white;
            cursor: pointer;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .footer{
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .footer-icons{
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        .footer-icons > i{
            font-size: 1.2rem;
            cursor: pointer;
        }
        .navigation-options{
            display: flex;
            flex-direction: column;
            gap: 10px;
            flex: 0.7;
            justify-content: flex-start;
            align-items: flex-start;
        }
        .logout-icon{
            display: flex;
            flex: 0.3;
            align-items: flex-start;
            gap: 10px;
            cursor: pointer;
        }

        .search-bar{
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 0.7;
            border: 1px solid #ccc;
            border-radius: 10px;
            padding: 10px;
            background-color: white;
            cursor: pointer;
        }
        .search-bar > input{
            border: none;
            outline: none;
            background-color: transparent;
            flex: 1;
        }
        .cart-prod-icon{
            display: flex;
            align-items: center;
            gap: 20px;
            cursor: pointer;
        }
        p{
            color: var(--text-color);
        }
        i{
            font-size: 1.4rem;
        }
        .profile{
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-layout {
                flex-direction: column;
            }

            .sidebar {
                height: auto;
                position: static;
                border-right: none;
                border-bottom: 1px solid #ccc;
            }

            .collection-cards {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }

            .scroll-content > .card {
                min-width: 250px;
                max-width: 250px;
            }

            .search-bar {
                flex: 1;
            }
        }

        @media (max-width: 480px) {
            .collection-cards {
                grid-template-columns: 1fr;
            }

            .scroll-content > .card {
                min-width: 200px;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <section class="main-layout">

        <!-- sidebar -->

        <aside class="sidebar">
                <div class="side-bar-header header-flex">
                    pickle paradise
                    <img class="profile" src="./assets/image.png" alt="Profile">
                </div>
                <div class="navigation-options">
                    <div class="option">
                        <i class="fa fa-home" aria-hidden="true"></i>
                        Home</div>
                    <div class="option">
                        <i class="fa fa-list" aria-hidden="true"></i>
                        All Pickles</div>
                    <div class="option">
                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                        Shopping Cart</div>
                </div>
                <div class="logout-icon">
                    <i class="fa fa-sign-out" aria-hidden="true"></i>
                    Logout
                </div>
        </aside>

        <!-- main-content -->
         <div class="main-content">
            <div class="nav-bar">
                <nav>
                    <nav-header class="header-flex">
                        <i class="fa fa-bars" style="font-size: 24px;display: none;" aria-hidden="true"></i>
                        <img class="profile" src="./assets/image.png" alt="Profile">
                        <p>pickle paradise</p>

                    </nav-header>
                    <div class="search-bar">
                        <input type="text" placeholder="Search...">
                        <i class="fa fa-search" aria-hidden="true"></i>
                    </div>
                    <div class="cart-prod-icon">
                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                        <i class="fa fa-user" aria-hidden="true"></i>
                    </div>
                </nav>
            </div>
            <main class="main-content-body">
                <section class="hero background-image">
                <img src="./assets/lovepik-pickled-pickled-vegetable-png-image_400262955_wh1200.png" alt="Pickled Vegetables Hero Image">
                <div class="overlay">
                    <h1>A New World of Pickles</h1>
                    <p>Experience the crunch, the flavor, the paradise. Hand-brined, all-natural, and unbelievably delicious.</p>
                    <button class="primary-button">See All Pickles<span><i class="fa fa-arrow-right" aria-hidden="true"></i></span></button>
                </div>
                </section>
                <section class="carousel">
                    <div class="right-arrow">
                        <i class="fa fa-arrow-left" aria-hidden="true"></i>
                    </div>
                    <div class="scroll-content">
                        <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png" alt="Mango Pickle">
                            <div class="card-body overlay">
                                <h3 class="card-title">Mango Pickle</h3>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <button class="primary-button">See More</button>
                            </div>
                        </div>
                         <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png" alt="Mango Pickle">
                            <div class="card-body overlay">
                                <h3 class="card-title">Mango Pickle</h3>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <button class="primary-button">See More</button>
                            </div>
                        </div>
                         <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png" alt="Mango Pickle">
                            <div class="card-body overlay">
                                <h3 class="card-title">Mango Pickle</h3>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <button class="primary-button">See More</button>
                            </div>
                        </div>
                    </div>
                    <div class="left-arrow">
                        <i class="fa fa-arrow-right" aria-hidden="true"></i>
                    </div>
                </section>
                <section class="collection grid">
                    <h2>Our Pickle Collection</h2>
                    <div class="collection-cards">
                        <div class="card">
                            <img class="background-image" src="./assets/lovepik-pickled-pickled-vegetable-png-image_400262955_wh1200.png" alt="Pickled Vegetables">
                            <div class="card-body overlay">
                                <h3 class="card-title">Mango Pickle</h3>
                                <div class="stars">
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                </div>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <div class="bottom-product-section">
                                    <div class="price">
                                        <p>Price: <span>$10</span></p>
                                    </div>
                                    <div class="add-to-cart primary-button">
                                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                                        Add to Cart
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png" alt="Mango Pickle">
                            <div class="card-body overlay">
                                <h3 class="card-title">Mango Pickle</h3>
                                <div class="stars">
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                </div>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <div class="bottom-product-section">
                                    <div class="price">
                                        <p>Price: <span>$10</span></p>
                                    </div>
                                    <div class="add-to-cart primary-button">
                                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                                        Add to Cart
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png" alt="Mango Pickle">
                            <div class="card-body overlay">
                                <h3 class="card-title">Mango Pickle</h3>
                                <div class="stars">
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                </div>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <div class="bottom-product-section">
                                    <div class="price">
                                        <p>Price: <span>$10</span></p>
                                    </div>
                                    <div class="add-to-cart primary-button">
                                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                                        Add to Cart
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png" alt="Mango Pickle">
                            <div class="card-body overlay">
                                <h3 class="card-title">Mango Pickle</h3>
                                <div class="stars">
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                </div>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <div class="bottom-product-section">
                                    <div class="price">
                                        <p>Price: <span>$10</span></p>
                                    </div>
                                    <div class="add-to-cart primary-button">
                                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                                        Add to Cart
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png" alt="Mango Pickle">
                            <div class="card-body overlay">
                                <h3 class="card-title">Mango Pickle</h3>
                                <div class="stars">
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                </div>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <div class="bottom-product-section">
                                    <div class="price">
                                        <p>Price: <span>$10</span></p>
                                    </div>
                                    <div class="add-to-cart primary-button">
                                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                                        Add to Cart
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png" alt="Mango Pickle">
                            <div class="card-body overlay">
                                <h3 class="card-title">Mango Pickle</h3>
                                <div class="stars">
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                </div>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <div class="bottom-product-section">
                                    <div class="price">
                                        <p>Price: <span>$10</span></p>
                                    </div>
                                    <div class="add-to-cart primary-button">
                                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                                        Add to Cart
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <footer class="footer">
                    <h2 class="card-title">
                        From Our Brine To Your Plate
                    </h2>
                    <p>Hand-brined, all-natural, and unbelievably delicious.</p>
                    <div class="footer-icons">
                        <i class="fa fa-facebook" aria-hidden="true"></i>
                        <i class="fa fa-twitter" aria-hidden="true"></i>
                        <i class="fa fa-instagram" aria-hidden="true"></i>
                    </div>
                </footer>
            </main>
         </div>
            <!-- carousel -->
            <!-- collection -->
            <!-- footer -->

        <!-- main-content -->
    </section>
</body>
</html>