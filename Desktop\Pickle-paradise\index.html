<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pickle Paradise</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="styles.css">
    <script src="script.js" defer></script>
</head>
<body>
    <section class="main-layout">

        <!-- sidebar -->

        <aside class="sidebar">
                <div class="side-bar-header header-flex">
                    pickle paradise
                    <img class="profile" src="./assets/image.png" alt="Profile">
                    <i class="fa fa-times" style="font-size: 24px; margin-left: 10px;display: none;" data-menu-close=".sidebar" aria-hidden="true"></i>
                </div>
                <div class="navigation-options">
                    <div class="option">
                        <i class="fa fa-home" aria-hidden="true"></i>
                        Home</div>
                    <div class="option">
                        <i class="fa fa-list" aria-hidden="true"></i>
                        All Pickles</div>
                    <div class="option">
                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                        Shopping Cart</div>
                </div>
                <div class="logout-icon">
                    <i class="fa fa-sign-out" aria-hidden="true"></i>
                    Logout
                </div>
        </aside>

        <!-- main-content -->
         <div class="main-content">
            <header class="nav-bar">
                <nav>
                    <div class="nav-header header-flex">
                        <i class="fa fa-bars" style="font-size: 24px;display: none;" data-menu=".sidebar" aria-hidden="true"></i>
                        <img class="profile"

                             src="./assets/image.png"
                             alt="Profile"
                             loading="lazy"
                             onerror="this.classList.add('error-state'); this.alt='Profile image failed to load';">
                        <p>pickle paradise</p>
                    </div>
                    <div class="search-bar">
                        <input type="text" placeholder="Search...">
                        <i class="fa fa-search" aria-hidden="true"></i>
                    </div>
                    <div class="cart-prod-icon">
                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                        <div class="drop-down-class">
                        <i class="fa fa-user" data-toggle=".dropdown" aria-hidden="true"></i>
                            <div class="dropdown">
                        <ul class="dropbtn">
                            <li><span><i class="fa fa-caret-down" aria-hidden="true"></i></span><p>Write To Us</p> </li>
                            <li data-toggle=".sub-menu"><span><i class="fa fa-caret-down" aria-hidden="true"></i></span><p>Profile</p>
                                <div class="sub-menu dropdown">
                                    <ul>
                                        <li><p>My Profile</p></li>
                                        <li><p>My Orders</p></li>
                                        <li><p>My Wishlist</p></li>
                                    </ul>
                                </div>
                             </li>
                            <li><span><i class="fa fa-caret-down" aria-hidden="true"></i></span><p>Shipping & Returns</p> </li>
                            <li><span><i class="fa fa-caret-down" aria-hidden="true"></i></span><p>Review Us</p></li>
                        </ul>
                    </div>
                        </div>
                    </div>
                </nav>
            </header>
            <main class="main-content-body">
                <section class="hero background-image">
                <img src="./assets/lovepik-pickled-pickled-vegetable-png-image_400262955_wh1200.png"
                     alt="Pickled Vegetables Hero Image"
                     loading="lazy"
                     onerror="this.classList.add('error-state'); this.alt='Image failed to load';">
                <div class="overlay">
                    <h1>A New World of Pickles</h1>
                    <p>Experience the crunch, the flavor, the paradise. Hand-brined, all-natural, and unbelievably delicious.</p>
                    <button class="primary-button">See All Pickles<span><i class="fa fa-arrow-right" aria-hidden="true"></i></span></button>
                </div>
                </section>
                <section class="carousel">
                    <div class="right-arrow">
                        <i class="fa fa-arrow-left" aria-hidden="true"></i>
                    </div>
                    <div class="scroll-content">
                        <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png"
                                 alt="Mango Pickle"
                                 loading="lazy"
                                 onerror="this.classList.add('error-state'); this.alt='Product image failed to load';">
                            <div class="card-body overlay">
                                <h3 class="card-title">Mango Pickle</h3>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <button class="primary-button">See More</button>
                            </div>
                        </div>
                         <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png"
                                 alt="Mango Pickle"
                                 loading="lazy"
                                 onerror="this.classList.add('error-state'); this.alt='Product image failed to load';">
                            <div class="card-body overlay">
                                <h3 class="card-title">Mango Pickle</h3>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <button class="primary-button">See More</button>
                            </div>
                        </div>
                         <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png"
                                 alt="Mango Pickle"
                                 loading="lazy"
                                 onerror="this.classList.add('error-state'); this.alt='Product image failed to load';">
                            <div class="card-body overlay">
                                <h3 class="card-title">Mango Pickle</h3>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <button class="primary-button">See More</button>
                            </div>
                        </div>
                         <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png"
                                 alt="Mango Pickle"
                                 loading="lazy"
                                 onerror="this.classList.add('error-state'); this.alt='Product image failed to load';">
                            <div class="card-body overlay">
                                <h3 class="card-title">Mango Pickle</h3>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <button class="primary-button">See More</button>
                            </div>
                        </div>
                         <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png"
                                 alt="Spicy Mango Pickle"
                                 loading="lazy"
                                 onerror="this.classList.add('error-state'); this.alt='Product image failed to load';">
                            <div class="card-body overlay">
                                <h3 class="card-title">Spicy Mango Pickle</h3>
                                <p>Extra spicy mango pickle for those who love heat.</p>
                                <button class="primary-button">See More</button>
                            </div>
                        </div>
                         <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png"
                                 alt="Sweet Mango Pickle"
                                 loading="lazy"
                                 onerror="this.classList.add('error-state'); this.alt='Product image failed to load';">
                            <div class="card-body overlay">
                                <h3 class="card-title">Sweet Mango Pickle</h3>
                                <p>Sweet and tangy mango pickle perfect for any meal.</p>
                                <button class="primary-button">See More</button>
                            </div>
                        </div>
                    </div>
                    <div class="left-arrow">
                        <i class="fa fa-arrow-right" aria-hidden="true"></i>
                    </div>
                </section>
                <section class="collection grid">
                    <h2>Our Pickle Collection</h2>
                    <div class="collection-cards">
                        <div class="card">
                            <img class="background-image"
                                 src="./assets/lovepik-pickled-pickled-vegetable-png-image_400262955_wh1200.png"
                                 alt="Pickled Vegetables"
                                 loading="lazy"
                                 onerror="this.classList.add('error-state'); this.alt='Product image failed to load';">
                            <div class="card-body overlay">
                                <h3 class="card-title">Mango Pickle</h3>
                                <div class="stars">
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                </div>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <div class="bottom-product-section">
                                    <div class="price">
                                        <p>Price: <span>$10</span></p>
                                    </div>
                                    <div class="add-to-cart primary-button">
                                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                                        Add to Cart
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png"
                                 alt="Traditional Mango Pickle"
                                 loading="lazy"
                                 onerror="this.classList.add('error-state'); this.alt='Product image failed to load';">
                            <div class="card-body overlay">
                                <h3 class="card-title">Traditional Mango Pickle</h3>
                                <div class="stars">
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                </div>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <div class="bottom-product-section">
                                    <div class="price">
                                        <p>Price: <span>$10</span></p>
                                    </div>
                                    <div class="add-to-cart primary-button">
                                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                                        Add to Cart
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png"
                                 alt="Organic Mango Pickle"
                                 loading="lazy"
                                 onerror="this.classList.add('error-state'); this.alt='Product image failed to load';">
                            <div class="card-body overlay">
                                <h3 class="card-title">Organic Mango Pickle</h3>
                                <div class="stars">
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                </div>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <div class="bottom-product-section">
                                    <div class="price">
                                        <p>Price: <span>$10</span></p>
                                    </div>
                                    <div class="add-to-cart primary-button">
                                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                                        Add to Cart
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png"
                                 alt="Premium Mango Pickle"
                                 loading="lazy"
                                 onerror="this.classList.add('error-state'); this.alt='Product image failed to load';">
                            <div class="card-body overlay">
                                <h3 class="card-title">Premium Mango Pickle</h3>
                                <div class="stars">
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                </div>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <div class="bottom-product-section">
                                    <div class="price">
                                        <p>Price: <span>$10</span></p>
                                    </div>
                                    <div class="add-to-cart primary-button">
                                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                                        Add to Cart
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png"
                                 alt="Artisan Mango Pickle"
                                 loading="lazy"
                                 onerror="this.classList.add('error-state'); this.alt='Product image failed to load';">
                            <div class="card-body overlay">
                                <h3 class="card-title">Artisan Mango Pickle</h3>
                                <div class="stars">
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                </div>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <div class="bottom-product-section">
                                    <div class="price">
                                        <p>Price: <span>$10</span></p>
                                    </div>
                                    <div class="add-to-cart primary-button">
                                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                                        Add to Cart
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <img src="./assets/pngtree-mango-pickle-png-image_11437932.png"
                                 alt="Gourmet Mango Pickle"
                                 loading="lazy"
                                 onerror="this.classList.add('error-state'); this.alt='Product image failed to load';">
                            <div class="card-body overlay">
                                <h3 class="card-title">Gourmet Mango Pickle</h3>
                                <div class="stars">
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                    <i class="fa fa-star" aria-hidden="true"></i>
                                </div>
                                <p>Hand-brined mango pickle with a tangy and sweet flavor.</p>
                                <div class="bottom-product-section">
                                    <div class="price">
                                        <p>Price: <span>$10</span></p>
                                    </div>
                                    <div class="add-to-cart primary-button">
                                        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                                        Add to Cart
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <footer class="footer">
                    <h2 class="card-title">
                        From Our Brine To Your Plate
                    </h2>
                    <p>Hand-brined, all-natural, and unbelievably delicious.</p>
                    <div class="footer-icons">
                        <i class="fa fa-facebook" aria-hidden="true"></i>
                        <i class="fa fa-twitter" aria-hidden="true"></i>
                        <i class="fa fa-instagram" aria-hidden="true"></i>
                    </div>
                </footer>
            </main>
        </div>
    </div>

    <script>
        // Image loading and error handling
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('img');

            images.forEach(img => {
                // Add loading class initially
                img.classList.add('loading');

                // Handle successful load
                img.addEventListener('load', function() {
                    this.classList.remove('loading', 'image-loading');
                });

                // Handle load error
                img.addEventListener('error', function() {
                    this.classList.remove('loading', 'image-loading');
                    this.classList.add('error-state');
                    this.alt = 'Image failed to load';

                    // Create fallback content
                    const fallback = document.createElement('div');
                    fallback.className = 'image-fallback error-state';
                    fallback.innerHTML = '<i class="fa fa-image"></i><span>Image not available</span>';
                    fallback.style.cssText = `
                        width: 100%;
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        background-color: #f8f8f8;
                        color: #666;
                        font-size: 0.9rem;
                        gap: 10px;
                    `;

                    // Replace image with fallback if it's in a card
                    if (this.closest('.card')) {
                        this.style.display = 'none';
                        this.parentNode.insertBefore(fallback, this);
                    }
                });

                // Add loading animation for images that haven't loaded yet
                if (!img.complete) {
                    img.classList.add('image-loading');
                }
            });

            // Search functionality
            const searchInput = document.querySelector('.search-bar input');
            if (searchInput) {
                searchInput.addEventListener('input', function(e) {
                    const searchTerm = e.target.value.toLowerCase();
                    const cards = document.querySelectorAll('.collection .card');

                    cards.forEach(card => {
                        const title = card.querySelector('.card-title');
                        if (title) {
                            const titleText = title.textContent.toLowerCase();
                            if (titleText.includes(searchTerm)) {
                                card.style.display = 'flex';
                            } else {
                                card.style.display = searchTerm === '' ? 'flex' : 'none';
                            }
                        }
                    });
                });
            }

            // Smooth scroll for carousel arrows
            const rightArrow = document.querySelector('.right-arrow');
            const leftArrow = document.querySelector('.left-arrow');
            const scrollContent = document.querySelector('.scroll-content');

            if (rightArrow && scrollContent) {
                rightArrow.addEventListener('click', function() {
                    scrollContent.scrollBy({
                        left: -300,
                        behavior: 'smooth'
                    });
                });
            }

            if (leftArrow && scrollContent) {
                leftArrow.addEventListener('click', function() {
                    scrollContent.scrollBy({
                        left: 300,
                        behavior: 'smooth'
                    });
                });
            }
        });
    </script>
</body>
</html>