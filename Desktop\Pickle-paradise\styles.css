/* CSS Variables */
:root{
    --primary-color: #F5F2E0;
    --secondary-color: rgb(53, 149, 53);
    --tertiary-color: #FFFFFF;
    --title-color: #090A0B;
    --text-color: #474a4d;
}

/* Reset and Base Styles */
*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body{
    overflow-x: hidden;
    color: var(--text-color);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
}

/* Typography */
h1, h2, h3{
    color: var(--title-color);
}

p{
    color: var(--text-color);
}

i{
    font-size: 1.4rem;
}

/* Layout Components */
.main-layout{
    width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    display: flex; 
}

.main-content{
    flex: 1;
    overflow-y: auto; 
    overflow-x: hidden;
    height: 100vh;
    position: relative;
}

/* Navigation */
.nav-bar{
    width: 100%;
    background-color: var(--primary-color);
    position: sticky;
    top: 0;
    z-index: 1;
    height: 60px;
}
.drop-down-class{
    position: relative;
}

.nav-bar>nav{
    max-width: 1200px;
    margin: 10px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
}

.header-flex{
    display: flex;
    align-items: center;
    gap: 10px;
}

.search-bar{
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 0.7;
    border: 1px solid #ccc;
    border-radius: 10px;
    padding: 10px;
    background-color: white;
    cursor: pointer;
}

.search-bar > input{
    border: none;
    outline: none;
    background-color: transparent;
    flex: 1;
}

.cart-prod-icon{
    display: flex;
    align-items: center;
    gap: 20px;
    cursor: pointer;
}

.profile{
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

/* Sidebar */
.sidebar{
    position: sticky;
    top: 0;
    background-color: var(--tertiary-color);
    display: flex;
    flex-direction: column;
    padding: 10px 20px;
    gap: 20px;
    border-right: 1px solid #ccc;
    height: 100vh;
}

.show-sidebar{
    left: 0 !important;
}

.navigation-options{
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 0.7;
    justify-content: flex-start;
    align-items: flex-start;
}

.option, .logout-icon{
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.2s ease;
}

.option:hover{
    background-color: #e6e6e6;
}

.logout-icon{
    flex: 0.3;
    position: absolute;
    bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
    cursor: pointer;
}

/* Buttons */
.primary-button{
    background-color: var(--secondary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: background-color 0.2s ease;
}

.primary-button:hover{
    background-color: rgb(45, 130, 45);
}

/* Hero Section */
.background-image{
    position: relative;
    height: 100vh;
    overflow: hidden;
    background-color: #f2f2f2;
}

.background-image>img{
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.overlay{
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-start;
    gap: 10px;
    padding: 20px;
    color: white;
}

.overlay p{
    font-size: 0.8rem;
    width: 90%;
}

/* Carousel */
.carousel{
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 20px;
    scroll-behavior: auto;
    scroll-snap-type: x mandatory;
    white-space: nowrap;
    position: relative;
}

.right-arrow, .left-arrow{
    width: 50px;
    height: 50px;
    background-color: #f2f2f2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    transition: background-color 0.2s ease;
}

.right-arrow:hover, .left-arrow:hover{
    background-color: #e0e0e0;
}

.right-arrow{
    left: 20px;
}

.left-arrow{
    right: 20px;
}

.scroll-content{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    scroll-snap-type: x mandatory;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
    /* white-space: nowrap; */
}

.scroll-content > .card{
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 300px;
    height: 400px;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
}

/* Collection */
.collection{
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    justify-content: center;
}

.collection-cards{
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    width: 100%;
    padding: 20px;
}

/* Cards */
.card{
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 300px;
    max-width: 300px;
    height: 400px;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    position: relative;
    transition: transform 0.2s ease;
}

.card:hover{
    transform: translateY(-5px);
}

.card > img{
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-body{
    padding: 20px;
    display: flex;
    overflow: hidden;
    flex-direction: column;
    gap: 10px;
    flex: 1;
    justify-content: flex-end;
}

.card-title{
    font-size: 1.2rem;
}

.bottom-product-section{
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.bottom-product-section>button{
    background-color: var(--secondary-color);
    border: none;
    color: white;
    cursor: pointer;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Footer */
.footer{
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.footer-icons{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.footer-icons > i{
    font-size: 1.2rem;
    cursor: pointer;
    transition: color 0.2s ease;
}

.footer-icons > i:hover{
    color: var(--secondary-color);
}

/* Loading States */
.loading {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.image-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}
.show{
   position: absolute;
    top: 120%;
    right: 100%;
    overflow: visible !important;
    width: max-content !important;
    height: max-content !important;
    display: flex;
    font-size: 12px;
    background-color: green;
    color: white;
    align-items: flex-start;
}
.show > ul{
    display: flex;
    padding: 10px;
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
    justify-content: center;
    list-style: none;
    padding: 10px;
    margin: 0;
}
.sub-menu{
   top: -35%;
   right: 100%;
}
.show >ul>li{
    display: flex;
    width: 100%;
    border-top: 0.5px solid white;
    position: relative;
    align-items: center;
    gap: 10px;
    padding: 3px 10px;
    cursor: pointer;
}
.show >ul>li:hover{
    background-color: #e6e6e6;
    color: black;
    transition: background-color 0.2s ease;
    border: 1px solid #ccc;
}  
.show >ul>li>p:visited{
    color: black;
    background-color: burlywood;
}
.show >ul>li>p{
    flex: 1;
    color: white
}
.dropdown{
    width: 0;
    padding: 0;
    height: 0;
    overflow: hidden;
    transition: width 0.3s ease, height 0.3s ease;

}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.error-state {
    background-color: #f8f8f8;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-layout {
        flex-direction: column;
    }

    .sidebar {
        position: absolute;
        top: 0;
        left: -100%;
        z-index: 1000;
        transition: left 0.8s ease;
    }
    .fa-bars{
        display: block !important;
    }

    .collection-cards {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .scroll-content > .card {
        min-width: 350px;
        max-width: 350px;
    }

    .search-bar {
        flex: 1;
    }

    .nav-bar>nav {
        padding: 0 10px;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .collection-cards {
        grid-template-columns: 1fr;
        padding: 10px;
    }
    .profile,.profile+p{
        display: none !important;
    }

    .scroll-content > .card {
        min-width: 200px;
        max-width: 200px;
    }

    .card {
        min-width: 250px;
        max-width: 250px;
    }

    .carousel {
        padding: 10px;
    }

    .collection {
        padding: 10px;
    }
}
